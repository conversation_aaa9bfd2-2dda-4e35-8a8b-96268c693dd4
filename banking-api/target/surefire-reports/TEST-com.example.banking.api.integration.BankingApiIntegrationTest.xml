<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.example.banking.api.integration.BankingApiIntegrationTest" time="13.39" tests="4" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/home/<USER>/code/banking-system/banking-api/target/test-classes:/home/<USER>/code/banking-system/banking-api/target/classes:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.2.0/spring-boot-starter-web-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.2.0/spring-boot-starter-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.2.0/spring-boot-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.2.0/spring-boot-autoconfigure-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.2.0/spring-boot-starter-logging-3.2.0.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.11/logback-classic-1.4.11.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.11/logback-core-1.4.11.jar:/home/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/log4j-to-slf4j-2.21.1.jar:/home/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1/log4j-api-2.21.1.jar:/home/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.9/jul-to-slf4j-2.0.9.jar:/home/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/home/<USER>/.m2/repository/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.2.0/spring-boot-starter-json-3.2.0.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.3/jackson-databind-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.3/jackson-annotations-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.3/jackson-core-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.3/jackson-datatype-jsr310-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.15.3/jackson-module-parameter-names-2.15.3.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.2.0/spring-boot-starter-tomcat-3.2.0.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.16/tomcat-embed-core-10.1.16.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.16/tomcat-embed-websocket-10.1.16.jar:/home/<USER>/.m2/repository/org/springframework/spring-web/6.1.1/spring-web-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-beans/6.1.1/spring-beans-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-webmvc/6.1.1/spring-webmvc-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-aop/6.1.1/spring-aop-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-context/6.1.1/spring-context-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-expression/6.1.1/spring-expression-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.2.0/spring-boot-starter-validation-3.2.0.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.16/tomcat-embed-el-10.1.16.jar:/home/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.1.Final/hibernate-validator-8.0.1.Final.jar:/home/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/home/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.3.Final/jboss-logging-3.5.3.Final.jar:/home/<USER>/.m2/repository/com/fasterxml/classmate/1.6.0/classmate-1.6.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.2.0/spring-boot-starter-actuator-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.2.0/spring-boot-actuator-autoconfigure-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.2.0/spring-boot-actuator-3.2.0.jar:/home/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.12.0/micrometer-observation-1.12.0.jar:/home/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.12.0/micrometer-commons-1.12.0.jar:/home/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.12.0/micrometer-jakarta9-1.12.0.jar:/home/<USER>/.m2/repository/io/micrometer/micrometer-core/1.12.0/micrometer-core-1.12.0.jar:/home/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/home/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/home/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-ui/2.2.0/springdoc-openapi-starter-webmvc-ui-2.2.0.jar:/home/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-api/2.2.0/springdoc-openapi-starter-webmvc-api-2.2.0.jar:/home/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-common/2.2.0/springdoc-openapi-starter-common-2.2.0.jar:/home/<USER>/.m2/repository/io/swagger/core/v3/swagger-core-jakarta/2.2.15/swagger-core-jakarta-2.2.15.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.13.0/commons-lang3-3.13.0.jar:/home/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations-jakarta/2.2.15/swagger-annotations-jakarta-2.2.15.jar:/home/<USER>/.m2/repository/io/swagger/core/v3/swagger-models-jakarta/2.2.15/swagger-models-jakarta-2.2.15.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.15.3/jackson-dataformat-yaml-2.15.3.jar:/home/<USER>/.m2/repository/org/webjars/swagger-ui/5.2.0/swagger-ui-5.2.0.jar:/home/<USER>/code/banking-system/banking-application/target/banking-application-1.0-SNAPSHOT.jar:/home/<USER>/.m2/repository/org/mindrot/jbcrypt/0.4/jbcrypt-0.4.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.2.0/spring-boot-starter-test-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.2.0/spring-boot-test-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.2.0/spring-boot-test-autoconfigure-3.2.0.jar:/home/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.8.0/json-path-2.8.0.jar:/home/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.9/slf4j-api-2.0.9.jar:/home/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.1/jakarta.xml.bind-api-4.0.1.jar:/home/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.2/jakarta.activation-api-2.1.2.jar:/home/<USER>/.m2/repository/net/minidev/json-smart/2.5.0/json-smart-2.5.0.jar:/home/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.0/accessors-smart-2.5.0.jar:/home/<USER>/.m2/repository/org/ow2/asm/asm/9.3/asm-9.3.jar:/home/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/home/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/home/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.1.1/mockito-junit-jupiter-5.1.1.jar:/home/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/home/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/home/<USER>/.m2/repository/org/springframework/spring-core/6.1.1/spring-core-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-jcl/6.1.1/spring-jcl-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-test/6.1.1/spring-test-6.1.1.jar:/home/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.9.2/junit-jupiter-5.9.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.10.1/junit-jupiter-api-5.10.1.jar:/home/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.10.1/junit-platform-commons-1.10.1.jar:/home/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.10.1/junit-jupiter-params-5.10.1.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.10.1/junit-jupiter-engine-5.10.1.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.10.1/junit-platform-engine-1.10.1.jar:/home/<USER>/.m2/repository/org/assertj/assertj-core/3.24.2/assertj-core-3.24.2.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.14.10/byte-buddy-1.14.10.jar:/home/<USER>/.m2/repository/org/mockito/mockito-core/5.1.1/mockito-core-5.1.1.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.14.10/byte-buddy-agent-1.14.10.jar:/home/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:"/>
    <property name="java.vm.vendor" value="Debian"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://tracker.debian.org/openjdk-17"/>
    <property name="user.timezone" value="Europe/Stockholm"/>
    <property name="org.jboss.logging.provider" value="slf4j"/>
    <property name="os.name" value="Linux"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="US"/>
    <property name="sun.boot.library.path" value="/usr/lib/jvm/java-17-openjdk-amd64/lib"/>
    <property name="sun.java.command" value="/home/<USER>/code/banking-system/banking-api/target/surefire/surefirebooter-20250625075039214_18.jar /home/<USER>/code/banking-system/banking-api/target/surefire 2025-06-25T07-50-35_984-jvmRun1 surefire-20250625075039214_16tmp surefire_1-20250625075039214_17tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/home/<USER>/code/banking-system/banking-api/target/test-classes:/home/<USER>/code/banking-system/banking-api/target/classes:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.2.0/spring-boot-starter-web-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.2.0/spring-boot-starter-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.2.0/spring-boot-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.2.0/spring-boot-autoconfigure-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.2.0/spring-boot-starter-logging-3.2.0.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.11/logback-classic-1.4.11.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.11/logback-core-1.4.11.jar:/home/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/log4j-to-slf4j-2.21.1.jar:/home/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1/log4j-api-2.21.1.jar:/home/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.9/jul-to-slf4j-2.0.9.jar:/home/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/home/<USER>/.m2/repository/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.2.0/spring-boot-starter-json-3.2.0.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.3/jackson-databind-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.3/jackson-annotations-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.3/jackson-core-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.3/jackson-datatype-jsr310-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.15.3/jackson-module-parameter-names-2.15.3.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.2.0/spring-boot-starter-tomcat-3.2.0.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.16/tomcat-embed-core-10.1.16.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.16/tomcat-embed-websocket-10.1.16.jar:/home/<USER>/.m2/repository/org/springframework/spring-web/6.1.1/spring-web-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-beans/6.1.1/spring-beans-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-webmvc/6.1.1/spring-webmvc-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-aop/6.1.1/spring-aop-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-context/6.1.1/spring-context-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-expression/6.1.1/spring-expression-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.2.0/spring-boot-starter-validation-3.2.0.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.16/tomcat-embed-el-10.1.16.jar:/home/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.1.Final/hibernate-validator-8.0.1.Final.jar:/home/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/home/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.3.Final/jboss-logging-3.5.3.Final.jar:/home/<USER>/.m2/repository/com/fasterxml/classmate/1.6.0/classmate-1.6.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.2.0/spring-boot-starter-actuator-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.2.0/spring-boot-actuator-autoconfigure-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.2.0/spring-boot-actuator-3.2.0.jar:/home/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.12.0/micrometer-observation-1.12.0.jar:/home/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.12.0/micrometer-commons-1.12.0.jar:/home/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.12.0/micrometer-jakarta9-1.12.0.jar:/home/<USER>/.m2/repository/io/micrometer/micrometer-core/1.12.0/micrometer-core-1.12.0.jar:/home/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/home/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/home/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-ui/2.2.0/springdoc-openapi-starter-webmvc-ui-2.2.0.jar:/home/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-api/2.2.0/springdoc-openapi-starter-webmvc-api-2.2.0.jar:/home/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-common/2.2.0/springdoc-openapi-starter-common-2.2.0.jar:/home/<USER>/.m2/repository/io/swagger/core/v3/swagger-core-jakarta/2.2.15/swagger-core-jakarta-2.2.15.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.13.0/commons-lang3-3.13.0.jar:/home/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations-jakarta/2.2.15/swagger-annotations-jakarta-2.2.15.jar:/home/<USER>/.m2/repository/io/swagger/core/v3/swagger-models-jakarta/2.2.15/swagger-models-jakarta-2.2.15.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.15.3/jackson-dataformat-yaml-2.15.3.jar:/home/<USER>/.m2/repository/org/webjars/swagger-ui/5.2.0/swagger-ui-5.2.0.jar:/home/<USER>/code/banking-system/banking-application/target/banking-application-1.0-SNAPSHOT.jar:/home/<USER>/.m2/repository/org/mindrot/jbcrypt/0.4/jbcrypt-0.4.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.2.0/spring-boot-starter-test-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.2.0/spring-boot-test-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.2.0/spring-boot-test-autoconfigure-3.2.0.jar:/home/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.8.0/json-path-2.8.0.jar:/home/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.9/slf4j-api-2.0.9.jar:/home/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.1/jakarta.xml.bind-api-4.0.1.jar:/home/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.2/jakarta.activation-api-2.1.2.jar:/home/<USER>/.m2/repository/net/minidev/json-smart/2.5.0/json-smart-2.5.0.jar:/home/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.0/accessors-smart-2.5.0.jar:/home/<USER>/.m2/repository/org/ow2/asm/asm/9.3/asm-9.3.jar:/home/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/home/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/home/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.1.1/mockito-junit-jupiter-5.1.1.jar:/home/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/home/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/home/<USER>/.m2/repository/org/springframework/spring-core/6.1.1/spring-core-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-jcl/6.1.1/spring-jcl-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-test/6.1.1/spring-test-6.1.1.jar:/home/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.9.2/junit-jupiter-5.9.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.10.1/junit-jupiter-api-5.10.1.jar:/home/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.10.1/junit-platform-commons-1.10.1.jar:/home/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.10.1/junit-jupiter-params-5.10.1.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.10.1/junit-jupiter-engine-5.10.1.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.10.1/junit-platform-engine-1.10.1.jar:/home/<USER>/.m2/repository/org/assertj/assertj-core/3.24.2/assertj-core-3.24.2.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.14.10/byte-buddy-1.14.10.jar:/home/<USER>/.m2/repository/org/mockito/mockito-core/5.1.1/mockito-core-5.1.1.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.14.10/byte-buddy-agent-1.14.10.jar:/home/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/home/<USER>"/>
    <property name="user.language" value="en"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/usr/lib/jvm/java-17-openjdk-amd64"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/home/<USER>/code/banking-system/banking-api"/>
    <property name="java.vm.compressedOopsMode" value="32-bit"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.headless" value="true"/>
    <property name="surefire.real.class.path" value="/home/<USER>/code/banking-system/banking-api/target/surefire/surefirebooter-20250625075039214_18.jar"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="17.0.15+6-Debian-1deb12u1"/>
    <property name="user.name" value="sebastiandavidlarsson"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="6.6.76-08024-gb30cb4a129c2"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="localRepository" value="/home/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugs.debian.org/openjdk-17"/>
    <property name="java.io.tmpdir" value="/tmp"/>
    <property name="java.version" value="17.0.15"/>
    <property name="user.dir" value="/home/<USER>/code/banking-system/banking-api"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="12605"/>
    <property name="CONSOLE_LOG_CHARSET" value="UTF-8"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/usr/java/packages/lib:/usr/lib/x86_64-linux-gnu/jni:/lib/x86_64-linux-gnu:/usr/lib/x86_64-linux-gnu:/usr/lib/jni:/lib:/usr/lib"/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Debian"/>
    <property name="java.vm.version" value="17.0.15+6-Debian-1deb12u1"/>
    <property name="java.specification.maintenance.version" value="1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="61.0"/>
    <property name="CONSOLE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss} - %msg%n"/>
    <property name="LOGGED_APPLICATION_NAME" value="[banking-api] "/>
  </properties>
  <testcase name="shouldHandleAuthenticationErrorsCorrectly" classname="com.example.banking.api.integration.BankingApiIntegrationTest" time="1.519">
    <system-out><![CDATA[2025-06-25 07:50:42 - Could not detect default configuration classes for test class [com.example.banking.api.integration.BankingApiIntegrationTest]: BankingApiIntegrationTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
2025-06-25 07:50:42 - Found @SpringBootConfiguration com.example.banking.api.BankingApiApplication for test class com.example.banking.api.integration.BankingApiIntegrationTest

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-06-25 07:50:42 - Starting BankingApiIntegrationTest using Java 17.0.15 with PID 12605 (started by sebastiandavidlarsson in /home/<USER>/code/banking-system/banking-api)
2025-06-25 07:50:42 - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-25 07:50:42 - No active profile set, falling back to 1 default profile: "default"
2025-06-25 07:50:42 - Refreshing org.springframework.web.context.support.GenericWebApplicationContext@********
2025-06-25 07:50:43 - Found banking application JAR in file system: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:43 - Banking application JAR located at: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:43 - 15 mappings in 'requestMappingHandlerMapping'
2025-06-25 07:50:43 - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-25 07:50:43 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-25 07:50:43 - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-25 07:50:44 - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-25 07:50:44 - Started BankingApiIntegrationTest in 1.201 seconds (process running for 4.817)
2025-06-25 07:50:44 - Initializing Spring TestDispatcherServlet ''
2025-06-25 07:50:44 - Initializing Servlet ''
2025-06-25 07:50:44 - Completed initialization in 1 ms
2025-06-25 07:50:44 - Mapped to com.example.banking.api.controller.BankingController#register(RegisterRequest)
2025-06-25 07:50:44 - Read "application/json" to [com.example.banking.api.dto.RegisterRequest@46a9c368]
2025-06-25 07:50:44 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:44 - Raw output: [
===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: New username: New password: Saved 1 users to storage.
Registration successful! You can now login.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: Thank you for using the Banking System!
Saving all data before exit...
Saved 1 users to storage.
]
2025-06-25 07:50:44 - Cleaned output: [
===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: New username: New password: Saved 1 users to storage.
Registration successful! You can now login.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: Thank you for using the Banking System!
Saving all data before exit...
Saved 1 users to storage.
]
2025-06-25 07:50:44 - Process output (cleaned): 
===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: New username: New password: Saved 1 users to storage.
Registration successful! You can now login.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: Thank you for using the Banking System!
Saving all data before exit...
Saved 1 users to storage.

2025-06-25 07:50:44 - Registration output: 
===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: New username: New password: Saved 1 users to storage.
Registration successful! You can now login.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: Thank you for using the Banking System!
Saving all data before exit...
Saved 1 users to storage.

2025-06-25 07:50:44 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:44 - Writing [com.example.banking.api.dto.ApiResponse@e23af91]
2025-06-25 07:50:44 - Mapped to com.example.banking.api.controller.BankingController#login(LoginRequest)
2025-06-25 07:50:44 - Read "application/json" to [com.example.banking.api.dto.LoginRequest@396ec737]
2025-06-25 07:50:44 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:44 - === AUTHENTICATION DEBUG START ===
2025-06-25 07:50:44 - Step 1: Waiting for initial menu...
2025-06-25 07:50:44 - Raw output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:44 - Step 1 - Raw initial output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:44 - Step 2: Sending login option (1)...
2025-06-25 07:50:44 - Step 3: Waiting for username prompt...
2025-06-25 07:50:44 - Raw output: [Username: ]
2025-06-25 07:50:44 - Step 3 - Raw username output: [Username: ]
2025-06-25 07:50:44 - Step 4: Sending username: testuser
2025-06-25 07:50:44 - Step 5: Waiting for password prompt...
2025-06-25 07:50:44 - Raw output: [Password: ]
2025-06-25 07:50:44 - Step 5 - Raw password output: [Password: ]
2025-06-25 07:50:44 - Step 6: Sending password...
2025-06-25 07:50:44 - Step 7: Waiting for authentication result...
2025-06-25 07:50:44 - Raw output: [Authentication failed. Invalid username or password.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:44 - Step 7 - Raw auth result: [Authentication failed. Invalid username or password.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:44 - Step 8: Analyzing authentication result...
2025-06-25 07:50:44 - Authentication analysis - Welcome: false, Username: false, Successful: false, LoggedIn: false, BankingMenu: false, AccountMenu: false, Invalid: true, Failed: true, AccessDenied: true
2025-06-25 07:50:44 - Step 8 - Authentication successful: false
2025-06-25 07:50:44 - Step 10: Handling failed authentication...
2025-06-25 07:50:44 - === AUTHENTICATION DEBUG END - FAILURE ===
2025-06-25 07:50:44 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:44 - Nothing to write: null body
2025-06-25 07:50:44 - Mapped to com.example.banking.api.controller.BankingController#deposit(TransactionRequest)
2025-06-25 07:50:44 - Read "application/json" to [com.example.banking.api.dto.TransactionRequest@5b16e486]
2025-06-25 07:50:44 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:44 - === DEPOSIT OPERATION DEBUG START ===
2025-06-25 07:50:44 - Step 1: Waiting for initial menu...
2025-06-25 07:50:44 - Raw output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:44 - Step 1 - Raw initial output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:44 - Step 2: Sending login option (1)...
2025-06-25 07:50:44 - Step 3: Waiting for username prompt...
2025-06-25 07:50:44 - Raw output: [Username: ]
2025-06-25 07:50:44 - Step 3 - Raw username output: [Username: ]
2025-06-25 07:50:44 - Step 4: Sending username: testuser
2025-06-25 07:50:44 - Step 5: Waiting for password prompt...
2025-06-25 07:50:44 - Raw output: [Password: ]
2025-06-25 07:50:44 - Step 5 - Raw password output: [Password: ]
2025-06-25 07:50:44 - Step 6: Sending password...
2025-06-25 07:50:44 - Step 7: Waiting for authentication result...
2025-06-25 07:50:45 - Raw output: [Authentication failed. Invalid username or password.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:45 - Step 7 - Raw auth result: [Authentication failed. Invalid username or password.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:45 - Step 8: Analyzing authentication result...
2025-06-25 07:50:45 - Authentication analysis - Welcome: false, Username: false, Successful: false, LoggedIn: false, BankingMenu: false, AccountMenu: false, Invalid: true, Failed: true, AccessDenied: true
2025-06-25 07:50:45 - Step 8 - Authentication successful: false
2025-06-25 07:50:45 - Step 8: Authentication failed, exiting...
2025-06-25 07:50:45 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:45 - Nothing to write: null body
2025-06-25 07:50:45 - Mapped to com.example.banking.api.controller.BankingController#getBalance(LoginRequest)
2025-06-25 07:50:45 - Read "application/json" to [com.example.banking.api.dto.LoginRequest@4d682397]
2025-06-25 07:50:45 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:45 - === BALANCE OPERATION DEBUG START ===
2025-06-25 07:50:45 - Step 1: Waiting for initial menu...
2025-06-25 07:50:45 - Raw output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:45 - Step 1 - Raw initial output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:45 - Step 2: Sending login option (1)...
2025-06-25 07:50:45 - Step 3: Waiting for username prompt...
2025-06-25 07:50:45 - Raw output: [Username: ]
2025-06-25 07:50:45 - Step 3 - Raw username output: [Username: ]
2025-06-25 07:50:45 - Step 4: Sending username: testuser
2025-06-25 07:50:45 - Step 5: Waiting for password prompt...
2025-06-25 07:50:45 - Raw output: [Password: ]
2025-06-25 07:50:45 - Step 5 - Raw password output: [Password: ]
2025-06-25 07:50:45 - Step 6: Sending password...
2025-06-25 07:50:45 - Step 7: Waiting for authentication result...
2025-06-25 07:50:45 - Raw output: [Authentication failed. Invalid username or password.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:45 - Step 7 - Raw auth result: [Authentication failed. Invalid username or password.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:45 - Step 8: Analyzing authentication result...
2025-06-25 07:50:45 - Authentication analysis - Welcome: false, Username: false, Successful: false, LoggedIn: false, BankingMenu: false, AccountMenu: false, Invalid: true, Failed: true, AccessDenied: true
2025-06-25 07:50:45 - Step 8 - Authentication successful: false
2025-06-25 07:50:45 - Step 8: Authentication failed, exiting...
2025-06-25 07:50:45 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:45 - Nothing to write: null body
]]></system-out>
  </testcase>
  <testcase name="shouldCompleteFullBankingWorkflow" classname="com.example.banking.api.integration.BankingApiIntegrationTest" time="10.129">
    <system-out><![CDATA[2025-06-25 07:50:45 - Initializing Spring TestDispatcherServlet ''
2025-06-25 07:50:45 - Initializing Servlet ''
2025-06-25 07:50:45 - Completed initialization in 2 ms
2025-06-25 07:50:45 - Mapped to com.example.banking.api.controller.BankingController#register(RegisterRequest)
2025-06-25 07:50:45 - Read "application/json" to [com.example.banking.api.dto.RegisterRequest@362e709e]
2025-06-25 07:50:45 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:45 - Raw output: [
===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: New username: New password: Saved 1 users to storage.
Registration successful! You can now login.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: Thank you for using the Banking System!
Saving all data before exit...
Saved 1 users to storage.
]
2025-06-25 07:50:45 - Cleaned output: [
===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: New username: New password: Saved 1 users to storage.
Registration successful! You can now login.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: Thank you for using the Banking System!
Saving all data before exit...
Saved 1 users to storage.
]
2025-06-25 07:50:45 - Process output (cleaned): 
===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: New username: New password: Saved 1 users to storage.
Registration successful! You can now login.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: Thank you for using the Banking System!
Saving all data before exit...
Saved 1 users to storage.

2025-06-25 07:50:45 - Registration output: 
===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: New username: New password: Saved 1 users to storage.
Registration successful! You can now login.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: Thank you for using the Banking System!
Saving all data before exit...
Saved 1 users to storage.

2025-06-25 07:50:45 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:45 - Writing [com.example.banking.api.dto.ApiResponse@b0c4905]
2025-06-25 07:50:45 - Mapped to com.example.banking.api.controller.BankingController#login(LoginRequest)
2025-06-25 07:50:45 - Read "application/json" to [com.example.banking.api.dto.LoginRequest@7d3a2459]
2025-06-25 07:50:45 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:45 - === AUTHENTICATION DEBUG START ===
2025-06-25 07:50:45 - Step 1: Waiting for initial menu...
2025-06-25 07:50:46 - Raw output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:46 - Step 1 - Raw initial output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:46 - Step 2: Sending login option (1)...
2025-06-25 07:50:46 - Step 3: Waiting for username prompt...
2025-06-25 07:50:46 - Raw output: [Username: ]
2025-06-25 07:50:46 - Step 3 - Raw username output: [Username: ]
2025-06-25 07:50:46 - Step 4: Sending username: integrationuser
2025-06-25 07:50:46 - Step 5: Waiting for password prompt...
2025-06-25 07:50:46 - Raw output: [Password: ]
2025-06-25 07:50:46 - Step 5 - Raw password output: [Password: ]
2025-06-25 07:50:46 - Step 6: Sending password...
2025-06-25 07:50:46 - Step 7: Waiting for authentication result...
2025-06-25 07:50:46 - Raw output: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:46 - Step 7 - Raw auth result: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:46 - Step 8: Analyzing authentication result...
2025-06-25 07:50:46 - Authentication analysis - Welcome: true, Username: true, Successful: false, LoggedIn: true, BankingMenu: true, AccountMenu: true, Invalid: false, Failed: false, AccessDenied: false
2025-06-25 07:50:46 - Step 8 - Authentication successful: true
2025-06-25 07:50:46 - Step 9: Attempting graceful logout...
2025-06-25 07:50:46 - Raw output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:46 - Step 9 - Logout output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:46 - === AUTHENTICATION DEBUG END - SUCCESS ===
2025-06-25 07:50:46 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:46 - Writing [com.example.banking.api.dto.UserResponse@3833897c]
2025-06-25 07:50:46 - Mapped to com.example.banking.api.controller.BankingController#getBalance(LoginRequest)
2025-06-25 07:50:46 - Read "application/json" to [com.example.banking.api.dto.LoginRequest@519eab1e]
2025-06-25 07:50:46 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:46 - === BALANCE OPERATION DEBUG START ===
2025-06-25 07:50:46 - Step 1: Waiting for initial menu...
2025-06-25 07:50:46 - Raw output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:46 - Step 1 - Raw initial output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:46 - Step 2: Sending login option (1)...
2025-06-25 07:50:46 - Step 3: Waiting for username prompt...
2025-06-25 07:50:46 - Raw output: [Username: ]
2025-06-25 07:50:46 - Step 3 - Raw username output: [Username: ]
2025-06-25 07:50:46 - Step 4: Sending username: integrationuser
2025-06-25 07:50:46 - Step 5: Waiting for password prompt...
2025-06-25 07:50:46 - Raw output: [Password: ]
2025-06-25 07:50:46 - Step 5 - Raw password output: [Password: ]
2025-06-25 07:50:46 - Step 6: Sending password...
2025-06-25 07:50:46 - Step 7: Waiting for authentication result...
2025-06-25 07:50:46 - Raw output: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:46 - Step 7 - Raw auth result: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:46 - Step 8: Analyzing authentication result...
2025-06-25 07:50:46 - Authentication analysis - Welcome: true, Username: true, Successful: false, LoggedIn: true, BankingMenu: true, AccountMenu: true, Invalid: false, Failed: false, AccessDenied: false
2025-06-25 07:50:46 - Step 8 - Authentication successful: true
2025-06-25 07:50:46 - Step 9: Waiting for banking menu...
2025-06-25 07:50:46 - Raw output: []
2025-06-25 07:50:46 - Step 9 - Banking menu output: []
2025-06-25 07:50:46 - Step 10: Sending list transactions option (3)...
2025-06-25 07:50:46 - Step 11: Waiting for transaction output...
2025-06-25 07:50:47 - Raw output: [No transactions to display.

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:47 - Step 11 - Transaction output: [No transactions to display.

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:47 - Step 12 - Parsed balance: 0.0
2025-06-25 07:50:47 - Step 13: Attempting graceful logout...
2025-06-25 07:50:47 - Raw output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:47 - Step 13 - Logout output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:47 - === BALANCE OPERATION DEBUG END - BALANCE: 0.0 ===
2025-06-25 07:50:47 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:47 - Writing [com.example.banking.api.dto.UserResponse@********]
2025-06-25 07:50:47 - Mapped to com.example.banking.api.controller.BankingController#deposit(TransactionRequest)
2025-06-25 07:50:47 - Read "application/json" to [com.example.banking.api.dto.TransactionRequest@415419a4]
2025-06-25 07:50:47 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:47 - === DEPOSIT OPERATION DEBUG START ===
2025-06-25 07:50:47 - Step 1: Waiting for initial menu...
2025-06-25 07:50:47 - Raw output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:47 - Step 1 - Raw initial output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:47 - Step 2: Sending login option (1)...
2025-06-25 07:50:47 - Step 3: Waiting for username prompt...
2025-06-25 07:50:47 - Raw output: [Username: ]
2025-06-25 07:50:47 - Step 3 - Raw username output: [Username: ]
2025-06-25 07:50:47 - Step 4: Sending username: integrationuser
2025-06-25 07:50:47 - Step 5: Waiting for password prompt...
2025-06-25 07:50:47 - Raw output: [Password: ]
2025-06-25 07:50:47 - Step 5 - Raw password output: [Password: ]
2025-06-25 07:50:47 - Step 6: Sending password...
2025-06-25 07:50:47 - Step 7: Waiting for authentication result...
2025-06-25 07:50:47 - Raw output: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:47 - Step 7 - Raw auth result: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:47 - Step 8: Analyzing authentication result...
2025-06-25 07:50:47 - Authentication analysis - Welcome: true, Username: true, Successful: false, LoggedIn: true, BankingMenu: true, AccountMenu: true, Invalid: false, Failed: false, AccessDenied: false
2025-06-25 07:50:47 - Step 8 - Authentication successful: true
2025-06-25 07:50:47 - Step 9: Waiting for banking menu...
2025-06-25 07:50:47 - Raw output: []
2025-06-25 07:50:47 - Step 9 - Banking menu output: []
2025-06-25 07:50:47 - Step 10: Sending deposit option (1)...
2025-06-25 07:50:47 - Step 11: Waiting for deposit amount prompt...
2025-06-25 07:50:47 - Raw output: [Enter amount to deposit: ]
2025-06-25 07:50:47 - Step 11 - Deposit prompt output: [Enter amount to deposit: ]
2025-06-25 07:50:47 - Step 12: Sending deposit amount: 100.0
2025-06-25 07:50:47 - Step 13: Waiting for deposit result...
2025-06-25 07:50:48 - Raw output: [Successfully deposited $100.0
Current Balance: $100.0
Saved 1 users to storage.

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:48 - Step 13 - Deposit result: [Successfully deposited $100.0
Current Balance: $100.0
Saved 1 users to storage.

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:48 - Step 14 - Deposit successful: true
2025-06-25 07:50:48 - Step 15: Attempting graceful logout...
2025-06-25 07:50:48 - Raw output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:48 - Step 15 - Logout output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:48 - === DEPOSIT OPERATION DEBUG END - SUCCESS: true ===
2025-06-25 07:50:48 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:48 - === BALANCE OPERATION DEBUG START ===
2025-06-25 07:50:48 - Step 1: Waiting for initial menu...
2025-06-25 07:50:48 - Raw output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:48 - Step 1 - Raw initial output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:48 - Step 2: Sending login option (1)...
2025-06-25 07:50:48 - Step 3: Waiting for username prompt...
2025-06-25 07:50:48 - Raw output: [Username: ]
2025-06-25 07:50:48 - Step 3 - Raw username output: [Username: ]
2025-06-25 07:50:48 - Step 4: Sending username: integrationuser
2025-06-25 07:50:48 - Step 5: Waiting for password prompt...
2025-06-25 07:50:48 - Raw output: [Password: ]
2025-06-25 07:50:48 - Step 5 - Raw password output: [Password: ]
2025-06-25 07:50:48 - Step 6: Sending password...
2025-06-25 07:50:48 - Step 7: Waiting for authentication result...
2025-06-25 07:50:48 - Raw output: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:48 - Step 7 - Raw auth result: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:48 - Step 8: Analyzing authentication result...
2025-06-25 07:50:48 - Authentication analysis - Welcome: true, Username: true, Successful: false, LoggedIn: true, BankingMenu: true, AccountMenu: true, Invalid: false, Failed: false, AccessDenied: false
2025-06-25 07:50:48 - Step 8 - Authentication successful: true
2025-06-25 07:50:48 - Step 9: Waiting for banking menu...
2025-06-25 07:50:48 - Raw output: []
2025-06-25 07:50:48 - Step 9 - Banking menu output: []
2025-06-25 07:50:48 - Step 10: Sending list transactions option (3)...
2025-06-25 07:50:48 - Step 11: Waiting for transaction output...
2025-06-25 07:50:49 - Raw output: [
===== Transaction History =====
[2025-06-25 07:50:47] Deposit: $100.00
Current Balance: $100.0

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:49 - Step 11 - Transaction output: [
===== Transaction History =====
[2025-06-25 07:50:47] Deposit: $100.00
Current Balance: $100.0

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:49 - Step 12 - Parsed balance: 100.0
2025-06-25 07:50:49 - Step 13: Attempting graceful logout...
2025-06-25 07:50:49 - Raw output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:49 - Step 13 - Logout output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:49 - === BALANCE OPERATION DEBUG END - BALANCE: 100.0 ===
2025-06-25 07:50:49 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:49 - Writing [com.example.banking.api.dto.TransactionResponse@6f12b637]
2025-06-25 07:50:49 - Mapped to com.example.banking.api.controller.BankingController#getBalance(LoginRequest)
2025-06-25 07:50:49 - Read "application/json" to [com.example.banking.api.dto.LoginRequest@eea0b48]
2025-06-25 07:50:49 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:49 - === BALANCE OPERATION DEBUG START ===
2025-06-25 07:50:49 - Step 1: Waiting for initial menu...
2025-06-25 07:50:49 - Raw output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:49 - Step 1 - Raw initial output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:49 - Step 2: Sending login option (1)...
2025-06-25 07:50:49 - Step 3: Waiting for username prompt...
2025-06-25 07:50:49 - Raw output: [Username: ]
2025-06-25 07:50:49 - Step 3 - Raw username output: [Username: ]
2025-06-25 07:50:49 - Step 4: Sending username: integrationuser
2025-06-25 07:50:49 - Step 5: Waiting for password prompt...
2025-06-25 07:50:49 - Raw output: [Password: ]
2025-06-25 07:50:49 - Step 5 - Raw password output: [Password: ]
2025-06-25 07:50:49 - Step 6: Sending password...
2025-06-25 07:50:49 - Step 7: Waiting for authentication result...
2025-06-25 07:50:49 - Raw output: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:49 - Step 7 - Raw auth result: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:49 - Step 8: Analyzing authentication result...
2025-06-25 07:50:49 - Authentication analysis - Welcome: true, Username: true, Successful: false, LoggedIn: true, BankingMenu: true, AccountMenu: true, Invalid: false, Failed: false, AccessDenied: false
2025-06-25 07:50:49 - Step 8 - Authentication successful: true
2025-06-25 07:50:49 - Step 9: Waiting for banking menu...
2025-06-25 07:50:49 - Raw output: []
2025-06-25 07:50:49 - Step 9 - Banking menu output: []
2025-06-25 07:50:49 - Step 10: Sending list transactions option (3)...
2025-06-25 07:50:49 - Step 11: Waiting for transaction output...
2025-06-25 07:50:50 - Raw output: [
===== Transaction History =====
[2025-06-25 07:50:47] Deposit: $100.00
Current Balance: $100.0

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:50 - Step 11 - Transaction output: [
===== Transaction History =====
[2025-06-25 07:50:47] Deposit: $100.00
Current Balance: $100.0

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:50 - Step 12 - Parsed balance: 100.0
2025-06-25 07:50:50 - Step 13: Attempting graceful logout...
2025-06-25 07:50:50 - Raw output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:50 - Step 13 - Logout output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:50 - === BALANCE OPERATION DEBUG END - BALANCE: 100.0 ===
2025-06-25 07:50:50 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:50 - Writing [com.example.banking.api.dto.UserResponse@6d5d1204]
2025-06-25 07:50:50 - Mapped to com.example.banking.api.controller.BankingController#withdraw(TransactionRequest)
2025-06-25 07:50:50 - Read "application/json" to [com.example.banking.api.dto.TransactionRequest@1784a296]
2025-06-25 07:50:50 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:50 - === WITHDRAWAL OPERATION DEBUG START ===
2025-06-25 07:50:50 - Step 1: Waiting for initial menu...
2025-06-25 07:50:50 - Raw output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:50 - Step 1 - Raw initial output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:50 - Step 2: Sending login option (1)...
2025-06-25 07:50:50 - Step 3: Waiting for username prompt...
2025-06-25 07:50:50 - Raw output: [Username: ]
2025-06-25 07:50:50 - Step 3 - Raw username output: [Username: ]
2025-06-25 07:50:50 - Step 4: Sending username: integrationuser
2025-06-25 07:50:50 - Step 5: Waiting for password prompt...
2025-06-25 07:50:50 - Raw output: [Password: ]
2025-06-25 07:50:50 - Step 5 - Raw password output: [Password: ]
2025-06-25 07:50:50 - Step 6: Sending password...
2025-06-25 07:50:50 - Step 7: Waiting for authentication result...
2025-06-25 07:50:50 - Raw output: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:50 - Step 7 - Raw auth result: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:50 - Step 8: Analyzing authentication result...
2025-06-25 07:50:50 - Authentication analysis - Welcome: true, Username: true, Successful: false, LoggedIn: true, BankingMenu: true, AccountMenu: true, Invalid: false, Failed: false, AccessDenied: false
2025-06-25 07:50:50 - Step 8 - Authentication successful: true
2025-06-25 07:50:50 - Step 9: Waiting for banking menu...
2025-06-25 07:50:50 - Raw output: []
2025-06-25 07:50:50 - Step 9 - Banking menu output: []
2025-06-25 07:50:50 - Step 10: Sending withdrawal option (2)...
2025-06-25 07:50:50 - Step 11: Waiting for withdrawal amount prompt...
2025-06-25 07:50:50 - Raw output: [Enter amount to withdraw: ]
2025-06-25 07:50:50 - Step 11 - Withdrawal prompt output: [Enter amount to withdraw: ]
2025-06-25 07:50:50 - Step 12: Sending withdrawal amount: 30.0
2025-06-25 07:50:50 - Step 13: Waiting for withdrawal result...
2025-06-25 07:50:50 - Raw output: [Successfully withdrew $30.0
Current Balance: $70.0
Saved 1 users to storage.

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:50 - Step 13 - Withdrawal result: [Successfully withdrew $30.0
Current Balance: $70.0
Saved 1 users to storage.

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:50 - Step 14 - Withdrawal successful: true
2025-06-25 07:50:50 - Step 15: Attempting graceful logout...
2025-06-25 07:50:50 - Raw output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:50 - Step 15 - Logout output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:50 - === WITHDRAWAL OPERATION DEBUG END - SUCCESS: true ===
2025-06-25 07:50:50 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:50 - === BALANCE OPERATION DEBUG START ===
2025-06-25 07:50:50 - Step 1: Waiting for initial menu...
2025-06-25 07:50:51 - Raw output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:51 - Step 1 - Raw initial output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:51 - Step 2: Sending login option (1)...
2025-06-25 07:50:51 - Step 3: Waiting for username prompt...
2025-06-25 07:50:51 - Raw output: [Username: ]
2025-06-25 07:50:51 - Step 3 - Raw username output: [Username: ]
2025-06-25 07:50:51 - Step 4: Sending username: integrationuser
2025-06-25 07:50:51 - Step 5: Waiting for password prompt...
2025-06-25 07:50:51 - Raw output: [Password: ]
2025-06-25 07:50:51 - Step 5 - Raw password output: [Password: ]
2025-06-25 07:50:51 - Step 6: Sending password...
2025-06-25 07:50:51 - Step 7: Waiting for authentication result...
2025-06-25 07:50:51 - Raw output: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:51 - Step 7 - Raw auth result: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:51 - Step 8: Analyzing authentication result...
2025-06-25 07:50:51 - Authentication analysis - Welcome: true, Username: true, Successful: false, LoggedIn: true, BankingMenu: true, AccountMenu: true, Invalid: false, Failed: false, AccessDenied: false
2025-06-25 07:50:51 - Step 8 - Authentication successful: true
2025-06-25 07:50:51 - Step 9: Waiting for banking menu...
2025-06-25 07:50:51 - Raw output: []
2025-06-25 07:50:51 - Step 9 - Banking menu output: []
2025-06-25 07:50:51 - Step 10: Sending list transactions option (3)...
2025-06-25 07:50:51 - Step 11: Waiting for transaction output...
2025-06-25 07:50:51 - Raw output: [
===== Transaction History =====
[2025-06-25 07:50:47] Deposit: $100.00
[2025-06-25 07:50:50] Withdrawal: $30.00
Current Balance: $70.0

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:51 - Step 11 - Transaction output: [
===== Transaction History =====
[2025-06-25 07:50:47] Deposit: $100.00
[2025-06-25 07:50:50] Withdrawal: $30.00
Current Balance: $70.0

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:51 - Step 12 - Parsed balance: 70.0
2025-06-25 07:50:51 - Step 13: Attempting graceful logout...
2025-06-25 07:50:51 - Raw output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:51 - Step 13 - Logout output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:51 - === BALANCE OPERATION DEBUG END - BALANCE: 70.0 ===
2025-06-25 07:50:51 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:51 - Writing [com.example.banking.api.dto.TransactionResponse@4f3e65f4]
2025-06-25 07:50:51 - Mapped to com.example.banking.api.controller.BankingController#getBalance(LoginRequest)
2025-06-25 07:50:51 - Read "application/json" to [com.example.banking.api.dto.LoginRequest@7d1cfe97]
2025-06-25 07:50:51 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:51 - === BALANCE OPERATION DEBUG START ===
2025-06-25 07:50:51 - Step 1: Waiting for initial menu...
2025-06-25 07:50:52 - Raw output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:52 - Step 1 - Raw initial output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:52 - Step 2: Sending login option (1)...
2025-06-25 07:50:52 - Step 3: Waiting for username prompt...
2025-06-25 07:50:52 - Raw output: [Username: ]
2025-06-25 07:50:52 - Step 3 - Raw username output: [Username: ]
2025-06-25 07:50:52 - Step 4: Sending username: integrationuser
2025-06-25 07:50:52 - Step 5: Waiting for password prompt...
2025-06-25 07:50:52 - Raw output: [Password: ]
2025-06-25 07:50:52 - Step 5 - Raw password output: [Password: ]
2025-06-25 07:50:52 - Step 6: Sending password...
2025-06-25 07:50:52 - Step 7: Waiting for authentication result...
2025-06-25 07:50:52 - Raw output: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:52 - Step 7 - Raw auth result: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:52 - Step 8: Analyzing authentication result...
2025-06-25 07:50:52 - Authentication analysis - Welcome: true, Username: true, Successful: false, LoggedIn: true, BankingMenu: true, AccountMenu: true, Invalid: false, Failed: false, AccessDenied: false
2025-06-25 07:50:52 - Step 8 - Authentication successful: true
2025-06-25 07:50:52 - Step 9: Waiting for banking menu...
2025-06-25 07:50:52 - Raw output: []
2025-06-25 07:50:52 - Step 9 - Banking menu output: []
2025-06-25 07:50:52 - Step 10: Sending list transactions option (3)...
2025-06-25 07:50:52 - Step 11: Waiting for transaction output...
2025-06-25 07:50:52 - Raw output: [
===== Transaction History =====
[2025-06-25 07:50:47] Deposit: $100.00
[2025-06-25 07:50:50] Withdrawal: $30.00
Current Balance: $70.0

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:52 - Step 11 - Transaction output: [
===== Transaction History =====
[2025-06-25 07:50:47] Deposit: $100.00
[2025-06-25 07:50:50] Withdrawal: $30.00
Current Balance: $70.0

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:52 - Step 12 - Parsed balance: 70.0
2025-06-25 07:50:52 - Step 13: Attempting graceful logout...
2025-06-25 07:50:52 - Raw output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:52 - Step 13 - Logout output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:52 - === BALANCE OPERATION DEBUG END - BALANCE: 70.0 ===
2025-06-25 07:50:52 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:52 - Writing [com.example.banking.api.dto.UserResponse@1822a61b]
2025-06-25 07:50:52 - Mapped to com.example.banking.api.controller.BankingController#getTransactions(LoginRequest)
2025-06-25 07:50:52 - Read "application/json" to [com.example.banking.api.dto.LoginRequest@240f712e]
2025-06-25 07:50:52 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:52 - === TRANSACTIONS OPERATION DEBUG START ===
2025-06-25 07:50:52 - Step 1: Waiting for initial menu...
2025-06-25 07:50:53 - Raw output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:53 - Step 1 - Raw initial output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:53 - Step 2: Sending login option (1)...
2025-06-25 07:50:53 - Step 3: Waiting for username prompt...
2025-06-25 07:50:53 - Raw output: [Username: ]
2025-06-25 07:50:53 - Step 3 - Raw username output: [Username: ]
2025-06-25 07:50:53 - Step 4: Sending username: integrationuser
2025-06-25 07:50:53 - Step 5: Waiting for password prompt...
2025-06-25 07:50:53 - Raw output: [Password: ]
2025-06-25 07:50:53 - Step 5 - Raw password output: [Password: ]
2025-06-25 07:50:53 - Step 6: Sending password...
2025-06-25 07:50:53 - Step 7: Waiting for authentication result...
2025-06-25 07:50:53 - Raw output: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:53 - Step 7 - Raw auth result: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:53 - Step 8: Analyzing authentication result...
2025-06-25 07:50:53 - Authentication analysis - Welcome: true, Username: true, Successful: false, LoggedIn: true, BankingMenu: true, AccountMenu: true, Invalid: false, Failed: false, AccessDenied: false
2025-06-25 07:50:53 - Step 8 - Authentication successful: true
2025-06-25 07:50:53 - Step 9: Waiting for banking menu...
2025-06-25 07:50:53 - Raw output: []
2025-06-25 07:50:53 - Step 9 - Banking menu output: []
2025-06-25 07:50:53 - Step 10: Sending list transactions option (3)...
2025-06-25 07:50:53 - Step 11: Waiting for transaction output...
2025-06-25 07:50:53 - Raw output: [
===== Transaction History =====
[2025-06-25 07:50:47] Deposit: $100.00
[2025-06-25 07:50:50] Withdrawal: $30.00
Current Balance: $70.0

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:53 - Step 11 - Transaction output: [
===== Transaction History =====
[2025-06-25 07:50:47] Deposit: $100.00
[2025-06-25 07:50:50] Withdrawal: $30.00
Current Balance: $70.0

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:53 - Step 12 - Parsed 2 transactions
2025-06-25 07:50:53 - Step 13: Attempting graceful logout...
2025-06-25 07:50:53 - Raw output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:53 - Step 13 - Logout output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:53 - === TRANSACTIONS OPERATION DEBUG END - COUNT: 2 ===
2025-06-25 07:50:53 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:53 - Writing [[com.example.banking.api.dto.TransactionResponse@198524ec, com.example.banking.api.dto.TransactionRe (truncated)...]
2025-06-25 07:50:53 - Mapped to com.example.banking.api.controller.BankingController#withdraw(TransactionRequest)
2025-06-25 07:50:53 - Read "application/json" to [com.example.banking.api.dto.TransactionRequest@6067e7c1]
2025-06-25 07:50:53 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:53 - === WITHDRAWAL OPERATION DEBUG START ===
2025-06-25 07:50:53 - Step 1: Waiting for initial menu...
2025-06-25 07:50:54 - Raw output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:54 - Step 1 - Raw initial output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:54 - Step 2: Sending login option (1)...
2025-06-25 07:50:54 - Step 3: Waiting for username prompt...
2025-06-25 07:50:54 - Raw output: [Username: ]
2025-06-25 07:50:54 - Step 3 - Raw username output: [Username: ]
2025-06-25 07:50:54 - Step 4: Sending username: integrationuser
2025-06-25 07:50:54 - Step 5: Waiting for password prompt...
2025-06-25 07:50:54 - Raw output: [Password: ]
2025-06-25 07:50:54 - Step 5 - Raw password output: [Password: ]
2025-06-25 07:50:54 - Step 6: Sending password...
2025-06-25 07:50:54 - Step 7: Waiting for authentication result...
2025-06-25 07:50:54 - Raw output: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:54 - Step 7 - Raw auth result: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:54 - Step 8: Analyzing authentication result...
2025-06-25 07:50:54 - Authentication analysis - Welcome: true, Username: true, Successful: false, LoggedIn: true, BankingMenu: true, AccountMenu: true, Invalid: false, Failed: false, AccessDenied: false
2025-06-25 07:50:54 - Step 8 - Authentication successful: true
2025-06-25 07:50:54 - Step 9: Waiting for banking menu...
2025-06-25 07:50:54 - Raw output: []
2025-06-25 07:50:54 - Step 9 - Banking menu output: []
2025-06-25 07:50:54 - Step 10: Sending withdrawal option (2)...
2025-06-25 07:50:54 - Step 11: Waiting for withdrawal amount prompt...
2025-06-25 07:50:54 - Raw output: [Enter amount to withdraw: ]
2025-06-25 07:50:54 - Step 11 - Withdrawal prompt output: [Enter amount to withdraw: ]
2025-06-25 07:50:54 - Step 12: Sending withdrawal amount: 100.0
2025-06-25 07:50:54 - Step 13: Waiting for withdrawal result...
2025-06-25 07:50:54 - Raw output: [Insufficient funds. Current balance: $70.0

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:54 - Step 13 - Withdrawal result: [Insufficient funds. Current balance: $70.0

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:54 - Step 14 - Withdrawal successful: false
2025-06-25 07:50:54 - Step 15: Attempting graceful logout...
2025-06-25 07:50:54 - Raw output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:54 - Step 15 - Logout output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:54 - === WITHDRAWAL OPERATION DEBUG END - SUCCESS: false ===
2025-06-25 07:50:54 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:54 - Nothing to write: null body
2025-06-25 07:50:54 - Mapped to com.example.banking.api.controller.BankingController#getBalance(LoginRequest)
2025-06-25 07:50:54 - Read "application/json" to [com.example.banking.api.dto.LoginRequest@43aa767]
2025-06-25 07:50:54 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:54 - === BALANCE OPERATION DEBUG START ===
2025-06-25 07:50:54 - Step 1: Waiting for initial menu...
2025-06-25 07:50:55 - Raw output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:55 - Step 1 - Raw initial output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:55 - Step 2: Sending login option (1)...
2025-06-25 07:50:55 - Step 3: Waiting for username prompt...
2025-06-25 07:50:55 - Raw output: [Username: ]
2025-06-25 07:50:55 - Step 3 - Raw username output: [Username: ]
2025-06-25 07:50:55 - Step 4: Sending username: integrationuser
2025-06-25 07:50:55 - Step 5: Waiting for password prompt...
2025-06-25 07:50:55 - Raw output: [Password: ]
2025-06-25 07:50:55 - Step 5 - Raw password output: [Password: ]
2025-06-25 07:50:55 - Step 6: Sending password...
2025-06-25 07:50:55 - Step 7: Waiting for authentication result...
2025-06-25 07:50:55 - Raw output: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:55 - Step 7 - Raw auth result: [Welcome, integrationuser!

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:55 - Step 8: Analyzing authentication result...
2025-06-25 07:50:55 - Authentication analysis - Welcome: true, Username: true, Successful: false, LoggedIn: true, BankingMenu: true, AccountMenu: true, Invalid: false, Failed: false, AccessDenied: false
2025-06-25 07:50:55 - Step 8 - Authentication successful: true
2025-06-25 07:50:55 - Step 9: Waiting for banking menu...
2025-06-25 07:50:55 - Raw output: []
2025-06-25 07:50:55 - Step 9 - Banking menu output: []
2025-06-25 07:50:55 - Step 10: Sending list transactions option (3)...
2025-06-25 07:50:55 - Step 11: Waiting for transaction output...
2025-06-25 07:50:55 - Raw output: [
===== Transaction History =====
[2025-06-25 07:50:47] Deposit: $100.00
[2025-06-25 07:50:50] Withdrawal: $30.00
Current Balance: $70.0

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:55 - Step 11 - Transaction output: [
===== Transaction History =====
[2025-06-25 07:50:47] Deposit: $100.00
[2025-06-25 07:50:50] Withdrawal: $30.00
Current Balance: $70.0

Welcome to Simple Banking App - Logged in as: integrationuser
1. Deposit
2. Withdraw
3. List Transactions
4. Logout
5. Exit Application
Please choose an option: ]
2025-06-25 07:50:55 - Step 12 - Parsed balance: 70.0
2025-06-25 07:50:55 - Step 13: Attempting graceful logout...
2025-06-25 07:50:55 - Raw output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:55 - Step 13 - Logout output: [Logged out successfully.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: ]
2025-06-25 07:50:55 - === BALANCE OPERATION DEBUG END - BALANCE: 70.0 ===
2025-06-25 07:50:55 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:55 - Writing [com.example.banking.api.dto.UserResponse@46573cfe]
]]></system-out>
  </testcase>
  <testcase name="shouldHandleDuplicateRegistrationCorrectly" classname="com.example.banking.api.integration.BankingApiIntegrationTest" time="0.415">
    <system-out><![CDATA[2025-06-25 07:50:55 - Initializing Spring TestDispatcherServlet ''
2025-06-25 07:50:55 - Initializing Servlet ''
2025-06-25 07:50:55 - Completed initialization in 1 ms
2025-06-25 07:50:55 - Mapped to com.example.banking.api.controller.BankingController#register(RegisterRequest)
2025-06-25 07:50:55 - Read "application/json" to [com.example.banking.api.dto.RegisterRequest@2f7d8a7c]
2025-06-25 07:50:55 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:55 - Raw output: [
===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: New username: New password: Saved 1 users to storage.
Registration successful! You can now login.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: Thank you for using the Banking System!
Saving all data before exit...
Saved 1 users to storage.
]
2025-06-25 07:50:55 - Cleaned output: [
===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: New username: New password: Saved 1 users to storage.
Registration successful! You can now login.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: Thank you for using the Banking System!
Saving all data before exit...
Saved 1 users to storage.
]
2025-06-25 07:50:55 - Process output (cleaned): 
===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: New username: New password: Saved 1 users to storage.
Registration successful! You can now login.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: Thank you for using the Banking System!
Saving all data before exit...
Saved 1 users to storage.

2025-06-25 07:50:55 - Registration output: 
===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: New username: New password: Saved 1 users to storage.
Registration successful! You can now login.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: Thank you for using the Banking System!
Saving all data before exit...
Saved 1 users to storage.

2025-06-25 07:50:55 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:55 - Writing [com.example.banking.api.dto.ApiResponse@14745c3c]
2025-06-25 07:50:55 - Mapped to com.example.banking.api.controller.BankingController#register(RegisterRequest)
2025-06-25 07:50:55 - Read "application/json" to [com.example.banking.api.dto.RegisterRequest@6bbb3280]
2025-06-25 07:50:55 - Starting banking process with JAR: /home/<USER>/code/banking-system/banking-api/../banking-application/target/banking-application-1.0-SNAPSHOT-jar-with-dependencies.jar
2025-06-25 07:50:56 - Raw output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: New username: New password: Username already exists. Please choose another one.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: Thank you for using the Banking System!
Saving all data before exit...
Saved 1 users to storage.
]
2025-06-25 07:50:56 - Cleaned output: [Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: New username: New password: Username already exists. Please choose another one.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: Thank you for using the Banking System!
Saving all data before exit...
Saved 1 users to storage.
]
2025-06-25 07:50:56 - Process output (cleaned): Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: New username: New password: Username already exists. Please choose another one.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: Thank you for using the Banking System!
Saving all data before exit...
Saved 1 users to storage.

2025-06-25 07:50:56 - Registration output: Loaded 1 users from storage.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: New username: New password: Username already exists. Please choose another one.

===== Banking System =====
1. Login
2. Register
3. Exit
Choose an option: Thank you for using the Banking System!
Saving all data before exit...
Saved 1 users to storage.

2025-06-25 07:50:56 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:56 - Writing [com.example.banking.api.dto.ApiResponse@7dda5b25]
]]></system-out>
  </testcase>
  <testcase name="shouldHandleValidationErrorsCorrectly" classname="com.example.banking.api.integration.BankingApiIntegrationTest" time="0.015">
    <system-out><![CDATA[2025-06-25 07:50:56 - Initializing Spring TestDispatcherServlet ''
2025-06-25 07:50:56 - Initializing Servlet ''
2025-06-25 07:50:56 - Completed initialization in 0 ms
2025-06-25 07:50:56 - Mapped to com.example.banking.api.controller.BankingController#register(RegisterRequest)
2025-06-25 07:50:56 - Read "application/json" to [com.example.banking.api.dto.RegisterRequest@8942ece]
2025-06-25 07:50:56 - Using @ExceptionHandler com.example.banking.api.exception.GlobalExceptionHandler#handleValidationExceptions(MethodArgumentNotValidException)
2025-06-25 07:50:56 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:56 - Writing [com.example.banking.api.dto.ApiResponse@2bfa5678]
2025-06-25 07:50:56 - Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public org.springframework.http.ResponseEntity<com.example.banking.api.dto.ApiResponse> com.example.banking.api.controller.BankingController.register(com.example.banking.api.dto.RegisterRequest) with 4 errors: [Field error in object 'registerRequest' on field 'password': rejected value []; codes [Size.registerRequest.password,Size.password,Size.java.lang.String,Size]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [registerRequest.password,password]; arguments []; default message [password],100,6]; default message [Password must be between 6 and 100 characters]] [Field error in object 'registerRequest' on field 'password': rejected value []; codes [NotBlank.registerRequest.password,NotBlank.password,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [registerRequest.password,password]; arguments []; default message [password]]; default message [Password is required]] [Field error in object 'registerRequest' on field 'username': rejected value []; codes [Size.registerRequest.username,Size.username,Size.java.lang.String,Size]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [registerRequest.username,username]; arguments []; default message [username],50,3]; default message [Username must be between 3 and 50 characters]] [Field error in object 'registerRequest' on field 'username': rejected value []; codes [NotBlank.registerRequest.username,NotBlank.username,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [registerRequest.username,username]; arguments []; default message [username]]; default message [Username is required]] ]
2025-06-25 07:50:56 - Mapped to com.example.banking.api.controller.BankingController#register(RegisterRequest)
2025-06-25 07:50:56 - Read "application/json" to [com.example.banking.api.dto.RegisterRequest@5b8b9b2d]
2025-06-25 07:50:56 - Using @ExceptionHandler com.example.banking.api.exception.GlobalExceptionHandler#handleValidationExceptions(MethodArgumentNotValidException)
2025-06-25 07:50:56 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:56 - Writing [com.example.banking.api.dto.ApiResponse@5f1db390]
2025-06-25 07:50:56 - Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public org.springframework.http.ResponseEntity<com.example.banking.api.dto.ApiResponse> com.example.banking.api.controller.BankingController.register(com.example.banking.api.dto.RegisterRequest): [Field error in object 'registerRequest' on field 'password': rejected value [123]; codes [Size.registerRequest.password,Size.password,Size.java.lang.String,Size]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [registerRequest.password,password]; arguments []; default message [password],100,6]; default message [Password must be between 6 and 100 characters]] ]
2025-06-25 07:50:56 - Mapped to com.example.banking.api.controller.BankingController#deposit(TransactionRequest)
2025-06-25 07:50:56 - Read "application/json" to [com.example.banking.api.dto.TransactionRequest@78c5ef58]
2025-06-25 07:50:56 - Using @ExceptionHandler com.example.banking.api.exception.GlobalExceptionHandler#handleValidationExceptions(MethodArgumentNotValidException)
2025-06-25 07:50:56 - Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-25 07:50:56 - Writing [com.example.banking.api.dto.ApiResponse@2b5ca574]
2025-06-25 07:50:56 - Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public org.springframework.http.ResponseEntity<com.example.banking.api.dto.TransactionResponse> com.example.banking.api.controller.BankingController.deposit(com.example.banking.api.dto.TransactionRequest): [Field error in object 'transactionRequest' on field 'amount': rejected value [-50.0]; codes [Positive.transactionRequest.amount,Positive.amount,Positive.double,Positive]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [transactionRequest.amount,amount]; arguments []; default message [amount]]; default message [Amount must be positive]] ]
]]></system-out>
  </testcase>
</testsuite>