server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: banking-api

  # Jackson configuration for JSON serialization
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

  # CORS configuration
  web:
    cors:
      allowed-origins:
        - "http://localhost:3000"
        - "http://127.0.0.1:3000"
        - "http://localhost:8081"
        - "http://127.0.0.1:8081"
      allowed-methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
      allowed-headers: "*"
      allow-credentials: true
      max-age: 3600

# Actuator configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# OpenAPI documentation
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method

# Banking Application Process Configuration
banking:
  application:
    jar-path: "../banking-application/target/banking-application-1.0-SNAPSHOT.jar"
    process-timeout: 30000  # 30 seconds timeout for process operations
    java-command: "java"

# Logging configuration
logging:
  level:
    com.example.banking.api: INFO
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
